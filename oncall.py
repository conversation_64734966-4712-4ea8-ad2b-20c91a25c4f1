import sys
import time
import datetime
import traceback
import copy
from pagerduty import PagerDuty
from rootly import Rootly
from slack import Slack
from slack_sdk import errors
import os
import logging
from format import logs_format
import redis

DATE_FORMAT = '%Y-%m-%dT%H:%M:%SZ'


def main():
    ENV_VARS = {
        "PD_TOKEN",
        "SLACK_ACCESS_TOKEN",
        "SLACK_USER_ACCESS_TOKEN",
        "INFRA_SERVICE_REDIS_DEFAULT_HOST",
        "INFRA_SERVICE_REDIS_DEFAULT_PORT",
        "INFRA_SERVICE_REDIS_DEFAULT_DB"
    }

    # Rootly environment variables
    ROOTLY_ENV_VARS = {
        "ROOTLY_API_TOKEN",
        "ROOTLY_SCHEDULE_ID",
        "ROOTLY_SLACK_GROUP"
    }
    ENV_VARS.update(ROOTLY_ENV_VARS)

    for var in ENV_VARS:
        if os.getenv(var) == None:
            raise Exception(
                f"{var} doesn't exist in environment variables. Please set it before run.")

    # LOGS
    if os.getenv('LOG_LEVEL'):
        LOG_LEVEL = logging.os.getenv('LOG_LEVEL')
    else:
        LOG_LEVEL = logging.INFO

    logging.basicConfig(level=LOG_LEVEL)
    cnt = 0
    while True:
        if cnt % 5:
            logging.info(logs_format(f'Running PagerDuty oncall logic'))
            OnCall().check_sre_oncall()

            # Run Rootly integration
            logging.info(logs_format(f'Running Rootly oncall logic'))
            RootlyOnCall().check_sre_oncall()

        logging.info(logs_format(f'Running maintenance windows logic'))
        Maintenance().maintenance_windows()
        time.sleep(int(os.getenv("LOOP_SECONDS", 60)))
        cnt += 1


class OnCall:
    def __init__(self):
        self.pager_duty = PagerDuty(os.getenv("PD_TOKEN"))
        self.slack = Slack(os.getenv("SLACK_USER_ACCESS_TOKEN"))

    def check_sre_oncall(self):

        pager_duty_escalation_policies_ids = self.pager_duty.get_escalation_policies_ids()
        pager_duty_escalation_policies_data = self.pager_duty.get_escalation_policies_data()

        for policy in pager_duty_escalation_policies_ids:
            esc_policy_name = f'{pager_duty_escalation_policies_data["esc_pol"][policy]["name"]}'
            logging.debug(logs_format(
                f'Get primary oncall user from Pager Duty "{esc_policy_name}" escalation policy.'))
            pager_duty_users = self.pager_duty.get_on_call(
                self.pager_duty.PRIMARY_ONCALL, policy)

            logging.debug(logs_format(
                f'"{pager_duty_escalation_policies_data["esc_pol"][policy]["name"]}" escalation policy.'))
            for group in pager_duty_escalation_policies_data["esc_pol"][policy]["slack_groups"]:
                logging.info(logs_format(
                    f'Checking that group members of "{group}" are oncall now'))
                slack_group = self.slack.get_group(group_name=group)

                slack_group_user_ids = self.slack.get_users_in_group(
                    group_id=slack_group["id"])

                pd_slack_user_ids = [
                    self.slack.get_user(user_email=pd_user["email"]).id
                    for pd_user in pager_duty_users
                ]
                logging.debug(logs_format(
                    f'Compare list of user that should be assigned with list of assigned users.'))
                logging.info(logs_format(
                    f'Slack user IDs from Pager Duty "{esc_policy_name}" escalation policy - "{pd_slack_user_ids}".'))
                logging.info(logs_format(
                    f'Slack user IDs from "{group}" Slack group  - "{pd_slack_user_ids}".'))
                if set(pd_slack_user_ids) and set(slack_group_user_ids) and set(pd_slack_user_ids) != set(slack_group_user_ids):
                    logging.info(logs_format(
                        f'Wrong set of users found in "{group}". Updating group'))
                    self.slack.set_group_users(
                        set(pd_slack_user_ids), slack_group["id"])


class RootlyOnCall:
    def __init__(self):
        self.rootly = Rootly(os.getenv("ROOTLY_API_TOKEN"))
        self.slack = Slack(os.getenv("SLACK_USER_ACCESS_TOKEN"))
        # Get the Slack group to update from environment variable
        self.slack_group = os.getenv("ROOTLY_SLACK_GROUP", "sreoncall-temp")
        self.schedule_id = os.getenv("ROOTLY_SCHEDULE_ID")

    def check_sre_oncall(self):
        """
        Updates the specified Slack group with on-call users from Rootly.
        Similar to the PagerDuty version but simplified for Rootly's API.
        """
        logging.info(logs_format(
            f'Getting on-call users from Rootly schedule {self.schedule_id}'))

        # Get on-call users from Rootly
        rootly_users = self.rootly.get_on_call_users(self.schedule_id)

        if not rootly_users:
            logging.warning(logs_format(
                f'No on-call users found in Rootly schedule {self.schedule_id}'))
            return

        # Get the Slack group
        logging.info(logs_format(
            f'Checking that group members of "{self.slack_group}" are on-call now'))
        slack_group = self.slack.get_group(group_name=self.slack_group)

        if not slack_group:
            logging.error(logs_format(
                f'Slack group "{self.slack_group}" not found'))
            return

        # Get current users in the Slack group
        slack_group_user_ids = self.slack.get_users_in_group(
            group_id=slack_group["id"])

        # Map Rootly users to Slack user IDs
        rootly_slack_user_ids = [
            self.slack.get_user(user_email=user["email"]).id
            for user in rootly_users
        ]

        logging.debug(logs_format(
            f'Compare list of users that should be assigned with list of assigned users.'))
        logging.info(logs_format(
            f'Slack user IDs from Rootly schedule {self.schedule_id} - "{rootly_slack_user_ids}".'))
        logging.info(logs_format(
            f'Slack user IDs from "{self.slack_group}" Slack group - "{slack_group_user_ids}".'))

        # Update the Slack group if the user sets are different
        if set(rootly_slack_user_ids) and set(slack_group_user_ids) and set(rootly_slack_user_ids) != set(slack_group_user_ids):
            logging.info(logs_format(
                f'Wrong set of users found in "{self.slack_group}". Updating group'))
            self.slack.set_group_users(
                set(rootly_slack_user_ids), slack_group["id"])


class Maintenance:
    def __init__(self):
        self.pager_duty = PagerDuty(os.getenv("PD_TOKEN"))
        self.slack = Slack(os.getenv("SLACK_ACCESS_TOKEN"))
        additional_slack_channels = os.getenv("SLACK_COMMON_CHANNELS")
        self.slack_common_channels = set()
        if additional_slack_channels:
            self.slack_common_channels = set(
                additional_slack_channels.split(","))

        pool = redis.ConnectionPool(host=os.getenv("INFRA_SERVICE_REDIS_DEFAULT_HOST"), port=int(
            os.getenv("INFRA_SERVICE_REDIS_DEFAULT_PORT")), db=os.getenv("INFRA_SERVICE_REDIS_DEFAULT_DB"))
        self.redis = redis.Redis(connection_pool=pool)

    def __maintenance(self, status, description):
        logging.info(
            f'Getting existing maintenance windows for status {status}')
        maintenance_windows = self.pager_duty.get_maintenance_windows(
            status=status)

        for maintenance_id, maintenance in maintenance_windows.items():
            # Skip processing if we have already processed newer items
            mnt_key = "latest_maintenance_%s" % maintenance_id
            latest_maintenance_status = self.redis.get(mnt_key)
            if latest_maintenance_status and latest_maintenance_status.decode('utf-8') == 'past':
                continue

            logging.info(
                f'Checking for already published message {maintenance_id}')
            for service in maintenance.get("services", {}):
                logging.info(
                    f'Getting a list of slack channels for service {service}')
                channels = self.pager_duty.get_slack_channels(service["id"])
                self.slack_common_channels.update(set(channels))

            if not self.slack_common_channels:
                logging.info(f'Length of slack channels is empty.')

            for channel in self.slack_common_channels:
                msg_key = f"notif_{maintenance_id}_{channel}"
                msg_key_hash = f"notif_hash_{maintenance_id}_{channel}"
                maintenance_text = self.slack.get_maintenance_announcement(
                    maintenance=maintenance, description=description)
                h = self.redis.get(msg_key_hash)
                if h and h.decode() == maintenance_text:
                    continue

                ts = self.redis.get(msg_key)
                ts = ts.decode() if ts else None
                try:
                    ts = self.slack.send_maintenance_announcement(
                        channel=channel, maintenance=maintenance, maintenance_text=maintenance_text, ts=ts)
                except errors.SlackApiError as e:
                    if e.response["error"] == "message_not_found":
                        logging.info(
                            f'Skipping msg update because of message_not_found %s %s %s', maintenance, ts, maintenance_text)
                    else:
                        logging.info(
                            f'Skipping msg update because error %s %s %s', e, maintenance, maintenance_text)
                        raise e

                self.redis.set(msg_key_hash, maintenance_text)
                logging.info(
                    f'Set redis msg msg_key_hash {msg_key_hash} {ts}')
                self.redis.set(msg_key, ts)
                logging.info(f'Set redis msg_key {msg_key} {ts}')

            self.redis.set(mnt_key, status)

    def maintenance_windows(self):
        # Create upcoming MW
        self.__maintenance(status="future", description="planned")
        # Set active ongoing MW
        self.__maintenance(status="ongoing", description="ongoing")
        # Close existing MW. Should be latest!!!!
        self.__maintenance(status="past", description="finished")


if __name__ == '__main__':
    main()
