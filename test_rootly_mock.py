#!/usr/bin/env python3
"""
Mock test script for Rootly API integration.
This script tests the Rootly class with mocked API responses to ensure it handles
different response formats correctly.
"""

import os
import sys
import json
import logging
import unittest
from unittest.mock import patch, MagicMock
from typing import List, Dict, Any

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the Rootly class
from rootly import Rootly
from format import logs_format

# Set up logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class MockResponse:
    """Mock response object for requests"""
    def __init__(self, json_data, status_code=200):
        self.json_data = json_data
        self.status_code = status_code
        self.ok = status_code < 400
    
    def json(self):
        return self.json_data
    
    def raise_for_status(self):
        if not self.ok:
            raise Exception(f"HTTP Error: {self.status_code}")

class TestRootly(unittest.TestCase):
    """Test cases for the Rootly class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a mock token
        self.token = "mock_token"
        
        # Create a mock schedule ID
        self.schedule_id = "mock_schedule_id"
        
        # Sample user data for the users cache
        self.mock_users_data = {
            "data": [
                {
                    "id": "user1",
                    "attributes": {
                        "name": "User One",
                        "email": "<EMAIL>"
                    }
                },
                {
                    "id": "user2",
                    "attributes": {
                        "name": "User Two",
                        "email": "<EMAIL>"
                    }
                }
            ]
        }
    
    @patch('requests.get')
    def test_normal_response(self, mock_get):
        """Test with a normal API response"""
        # Mock the users API call
        mock_get.side_effect = [
            MockResponse(self.mock_users_data),
            MockResponse({
                "data": [
                    {
                        "id": "oncall1",
                        "relationships": {
                            "owner_user": {
                                "data": {
                                    "id": "user1"
                                }
                            }
                        }
                    }
                ]
            })
        ]
        
        # Create a Rootly instance
        rootly = Rootly(self.token)
        
        # Get the on-call users
        users = rootly.get_on_call_users(self.schedule_id)
        
        # Check the results
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0]["id"], "user1")
        self.assertEqual(users[0]["email"], "<EMAIL>")
        self.assertEqual(users[0]["name"], "User One")
    
    @patch('requests.get')
    def test_string_response(self, mock_get):
        """Test with a string in the on_call data (the error case)"""
        # Mock the users API call
        mock_get.side_effect = [
            MockResponse(self.mock_users_data),
            MockResponse({
                "data": ["This is a string, not an object"]
            })
        ]
        
        # Create a Rootly instance
        rootly = Rootly(self.token)
        
        # Get the on-call users
        users = rootly.get_on_call_users(self.schedule_id)
        
        # Check the results - should handle the error and return empty list
        self.assertEqual(len(users), 0)
    
    @patch('requests.get')
    def test_single_object_response(self, mock_get):
        """Test with a single object instead of an array"""
        # Mock the users API call
        mock_get.side_effect = [
            MockResponse(self.mock_users_data),
            MockResponse({
                "data": {
                    "id": "oncall1",
                    "relationships": {
                        "owner_user": {
                            "data": {
                                "id": "user1"
                            }
                        }
                    }
                }
            })
        ]
        
        # Create a Rootly instance
        rootly = Rootly(self.token)
        
        # Get the on-call users
        users = rootly.get_on_call_users(self.schedule_id)
        
        # Check the results
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0]["id"], "user1")
    
    @patch('requests.get')
    def test_missing_relationships(self, mock_get):
        """Test with missing relationships field"""
        # Mock the users API call
        mock_get.side_effect = [
            MockResponse(self.mock_users_data),
            MockResponse({
                "data": [
                    {
                        "id": "oncall1"
                        # No relationships field
                    }
                ]
            })
        ]
        
        # Create a Rootly instance
        rootly = Rootly(self.token)
        
        # Get the on-call users
        users = rootly.get_on_call_users(self.schedule_id)
        
        # Check the results - should handle missing field and return empty list
        self.assertEqual(len(users), 0)

def main():
    """Run the tests"""
    unittest.main()

if __name__ == "__main__":
    main()
