#!/usr/bin/env python3
"""
Test script for Rootly API integration.
This script tests the Rootly class's ability to fetch on-call users from a schedule.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the Rootly class
from rootly import Rootly

# Set up logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_get_on_call_users(token: str, schedule_id: str) -> List[Dict[str, Any]]:
    """
    Test the get_on_call_users method of the Rootly class.
    
    Args:
        token: The Rootly API token
        schedule_id: The ID of the Rootly schedule to test
        
    Returns:
        The list of on-call users returned by the method
    """
    # Create a Rootly instance
    rootly = Rootly(token)
    
    # Get the on-call users
    users = rootly.get_on_call_users(schedule_id)
    
    # Print the results
    print(f"\nFound {len(users)} on-call users:")
    for user in users:
        print(f"  - {user['name']} ({user['email']})")
    
    return users

def main():
    """
    Main function to run the test.
    """
    # Check if token and schedule_id are provided as arguments
    if len(sys.argv) < 3:
        print("Usage: python test_rootly.py <token> <schedule_id>")
        print("Example: python test_rootly.py abc123 schedule_xyz")
        sys.exit(1)
    
    # Get the token and schedule_id from command line arguments
    token = sys.argv[1]
    schedule_id = sys.argv[2]
    
    print(f"Testing Rootly API with schedule ID: {schedule_id}")
    
    # Run the test
    test_get_on_call_users(token, schedule_id)
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
