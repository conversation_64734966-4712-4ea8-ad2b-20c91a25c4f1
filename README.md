# OnCall service

## Introduction

This service is responsible for some inspectorio oncall features in SRE team:

* Changing slack oncall group to current oncall engineers (from PagerDuty or Rootly)
* Announce and update Pager Duty maintenance windows to slack channel

## Required environment variables

### Core Environment Variables
* `PD_TOKEN` - Token (App Oncall) to access PagerDuty (can find at: https://inspectorio.pagerduty.com/api_keys#[https://inspectorio.pagerduty.com/api_keys#])
* `SLACK_ACCESS_TOKEN` & `SLACK_USER_ACCESS_TOKEN` - Token to access Slack
* `LOG_LEVEL` - Levels for logs output (INFO, DEBUG). If not set "INFO" will be used by default

### Rootly Integration
* `ROOTLY_API_TOKEN` - API token for Rootly authentication
* `ROOTLY_SCHEDULE_ID` - ID of the SRE-Primary schedule in Rootly
* `ROOTLY_SLACK_GROUP` - The Slack group to update (default: "sreoncall-temp")

## Slack Token

* Slack email account token ownership: `<EMAIL>`.
  * This slack account need to be in Workspace admin roles (ask IT to approve) to allow update slack user group.
* Slack app `Oncall`: https://api.slack.com/apps/A063HNHTE4C[https://api.slack.com/apps/A063HNHTE4C]
* We have two type of token:
  * User Token (SLACK_USER_ACCESS_TOKEN)
  * Bot Token (SLACK_ACCESS_TOKEN)
* Tracked at this document: https://inspectoriodocs.atlassian.net/wiki/spaces/SRE/pages/**********/Infrastructure+Services+Token+Ownership[https://inspectoriodocs.atlassian.net/wiki/spaces/SRE/pages/**********/Infrastructure+Services+Token+Ownership]

### Slack on-call function

* Slack on-call function will need slack `User Token` and below permissions scopes need for activity:
  * usergroups:read
  * usergroups:write
  * users.profile:read
  * users:read
  * users:read.email

### Slack maintenance function

* Slack maintenance function will need slack `Bot Token` and below permissions scopes need for activity
  * channels:read
  * chat:write
  * chat:write.customize
  * chat:write.public
  * links:write
  * usergroups:read
  * users.profile:read
  * users:read
  * users:read.email

## Migration from PagerDuty to Rootly

This service supports a phased migration from PagerDuty to Rootly for on-call management:

### Phase 1: Dual Operation
1. Configure Rootly to update a temporary Slack group (`@sreoncall-temp`) by setting `ROOTLY_SLACK_GROUP=sreoncall-temp`
2. PagerDuty continues to update the main `@sreoncall` group
3. Both systems run in parallel for validation

### Phase 2: Cutover
1. After validation period (approximately 1 month), update `ROOTLY_SLACK_GROUP=sreoncall`
2. Rootly will now update the main `@sreoncall` group
3. Optionally, disable PagerDuty integration when ready
