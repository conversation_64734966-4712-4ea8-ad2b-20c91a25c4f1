## Description

- Please include a summary of the Merge Request purpose/setup.

## Ticket(s)

- `link jira ticket`

## Type of change

Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New minor feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This change requires a documentation update

## Checklist before requesting a review

- [ ] I have performed a self-review of my code
- [ ] Jira ticket referenced in title, e.g: `JIRA-123: short header`
- [ ] CI checks are successful
- [ ] Apply a semantic versioning label to your Merge Request (patch, minor, major,.. ) depending on nature of change.

## Mention project owner

/cc @devops
/assign_reviewer @devops
/assign me
