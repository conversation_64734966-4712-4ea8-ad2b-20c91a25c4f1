include:
  - project: "devops/gitops-pipeline-templates"
    file: "generic-pipeline.yaml"

image: "asia.gcr.io/inspectorio-ant/python:3.9-without-nginx"

variables:
  CI_LINT_CHECKS: "false"
  DEV_LANGUAGE: python
  CI_SECURITY_STATIC_ANALYZER_CHECKS: "false"
  CI_SECURITY_CVE_CHECKS: "false"
  CI_SECURITY_GIT_SECRETS_CHECKS: "true"
  CI_SECURITY_STATIC_ANALYZER_CHECKS_EXTENDED: "false"
  APP_NAME: slack-oncall
  HELM_CHART: chart/
  CHART_TYPE: saas
