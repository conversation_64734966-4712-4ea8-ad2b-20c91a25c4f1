#!/usr/bin/env python3
"""
Test script for examining the raw Rootly API response.
This script helps debug issues by showing the exact structure of the API response.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the Rootly class
from rootly import Rootly

# Set up logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_api_response(token: str, schedule_id: str) -> Dict[str, Any]:
    """
    Test the Rootly API response for a schedule.
    
    Args:
        token: The Rootly API token
        schedule_id: The ID of the Rootly schedule to test
        
    Returns:
        The raw API response
    """
    # Create a Rootly instance
    rootly = Rootly(token)
    
    # Get the raw API response
    response = rootly.test_api_response(schedule_id)
    
    # Print the response in a readable format
    print("\nRaw API Response:")
    print(json.dumps(response, indent=2))
    
    # Check the structure of the response
    print("\nResponse Structure Analysis:")
    
    if not isinstance(response, dict):
        print(f"  - Response is not a dictionary, it's a {type(response).__name__}")
        return response
    
    # Check for data field
    if "data" not in response:
        print("  - Response does not contain a 'data' field")
    else:
        data = response["data"]
        print(f"  - 'data' field is of type {type(data).__name__}")
        
        if isinstance(data, list):
            print(f"  - 'data' is a list with {len(data)} items")
            
            # Check the first item if available
            if data:
                first_item = data[0]
                print(f"  - First item in 'data' is of type {type(first_item).__name__}")
                
                if isinstance(first_item, dict):
                    # Check for relationships
                    if "relationships" in first_item:
                        relationships = first_item["relationships"]
                        print(f"  - 'relationships' field is of type {type(relationships).__name__}")
                        
                        # Check for owner_user
                        if "owner_user" in relationships:
                            owner_user = relationships["owner_user"]
                            print(f"  - 'owner_user' field is of type {type(owner_user).__name__}")
                            
                            # Check for data in owner_user
                            if "data" in owner_user:
                                user_data = owner_user["data"]
                                print(f"  - 'data' field in owner_user is of type {type(user_data).__name__}")
                                
                                # Check for id
                                if isinstance(user_data, dict) and "id" in user_data:
                                    print(f"  - Found user ID: {user_data['id']}")
                                else:
                                    print("  - No user ID found in owner_user data")
                            else:
                                print("  - No 'data' field in owner_user")
                        else:
                            print("  - No 'owner_user' field in relationships")
                    else:
                        print("  - No 'relationships' field in first item")
                else:
                    print(f"  - First item in 'data' is not a dictionary: {first_item}")
        elif isinstance(data, dict):
            print("  - 'data' is a single object (dictionary)")
            
            # Check for relationships
            if "relationships" in data:
                relationships = data["relationships"]
                print(f"  - 'relationships' field is of type {type(relationships).__name__}")
                
                # Check for owner_user
                if "owner_user" in relationships:
                    owner_user = relationships["owner_user"]
                    print(f"  - 'owner_user' field is of type {type(owner_user).__name__}")
                    
                    # Check for data in owner_user
                    if "data" in owner_user:
                        user_data = owner_user["data"]
                        print(f"  - 'data' field in owner_user is of type {type(user_data).__name__}")
                        
                        # Check for id
                        if isinstance(user_data, dict) and "id" in user_data:
                            print(f"  - Found user ID: {user_data['id']}")
                        else:
                            print("  - No user ID found in owner_user data")
                    else:
                        print("  - No 'data' field in owner_user")
                else:
                    print("  - No 'owner_user' field in relationships")
            else:
                print("  - No 'relationships' field in data object")
        else:
            print(f"  - 'data' is neither a list nor a dictionary: {data}")
    
    return response

def main():
    """
    Main function to run the test.
    """
    # Check if token and schedule_id are provided as arguments
    if len(sys.argv) < 3:
        print("Usage: python test_rootly_api.py <token> <schedule_id>")
        print("Example: python test_rootly_api.py abc123 schedule_xyz")
        sys.exit(1)
    
    # Get the token and schedule_id from command line arguments
    token = sys.argv[1]
    schedule_id = sys.argv[2]
    
    print(f"Testing Rootly API with schedule ID: {schedule_id}")
    
    # Run the test
    test_api_response(token, schedule_id)
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
