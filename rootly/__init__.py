import requests
import logging
from format import logs_format
import os
import json
from typing import List, Dict, Any


class Rootly:
    """
    Class to interact with Rootly API for on-call schedules.
    """
    def __init__(self, token: str):
        self.__rootly_token = token
        self.api_url = "https://api.rootly.com/v1"
        self.headers = {
            "Authorization": f"Bearer {self.__rootly_token}",
            "Content-Type": "application/json"
        }
        self.users_map = {}
        self.__populate_users_cache()

    def __populate_users_cache(self):
        """
        Populate the users cache with Rootly users.
        Maps user IDs to user details including email.
        """
        logging.debug(logs_format("Populating Rootly users cache"))
        try:
            response = requests.get(f"{self.api_url}/users", headers=self.headers)
            response.raise_for_status()
            users = response.json().get("data", [])
            
            for user in users:
                user_id = user.get("id")
                user_attributes = user.get("attributes", {})
                self.users_map[user_id] = {
                    "username": user_attributes.get("name", ""),
                    "email": user_attributes.get("email", "")
                }
            
            logging.debug(logs_format(f"Cached {len(self.users_map)} Rootly users"))
        except requests.RequestException as e:
            logging.error(logs_format(f"Error fetching Rootly users: {str(e)}"))

    def get_on_call_users(self, schedule_id: str) -> List[Dict[str, Any]]:
        """
        Get the current on-call users for a specific schedule.
        
        Args:
            schedule_id: The ID of the Rootly schedule
            
        Returns:
            List of user objects with email and other details
        """
        logging.debug(logs_format(f"Getting on-call users from Rootly for schedule {schedule_id}"))
        result = []
        
        try:
            # Get current on-call users for the schedule
            response = requests.get(
                f"{self.api_url}/schedules/{schedule_id}", 
                headers=self.headers
            )
            response.raise_for_status()
            
            on_calls = response.json().get("data", [])
            for on_call in on_calls:
                user_id = on_call.get("relationships", {}).get("owner_user", {}).get("data", {}).get("id")
                if user_id and user_id in self.users_map:
                    # Format similar to PagerDuty's response for compatibility
                    user_info = {
                        "id": user_id,
                        "email": self.users_map[user_id]["email"],
                        "name": self.users_map[user_id]["username"]
                    }
                    result.append(user_info)
            
            logging.info(logs_format(f"Found {len(result)} on-call users in Rootly schedule {schedule_id}"))
            return result
            
        except requests.RequestException as e:
            logging.error(logs_format(f"Error fetching Rootly on-call users: {str(e)}"))
            return []
