import requests
import logging
from format import logs_format
import os
import json
from typing import List, Dict, Any


class Rootly:
    """
    Class to interact with Rootly API for on-call schedules.
    """
    def __init__(self, token: str):
        self.__rootly_token = token
        self.api_url = "https://api.rootly.com/v1"
        self.headers = {
            "Authorization": f"Bearer {self.__rootly_token}",
            "Content-Type": "application/json"
        }
        self.users_map = {}
        self.__populate_users_cache()

    def __populate_users_cache(self):
        """
        Populate the users cache with Rootly users.
        Maps user IDs to user details including email.
        """
        logging.debug(logs_format("Populating Rootly users cache"))
        try:
            response = requests.get(f"{self.api_url}/users", headers=self.headers)
            response.raise_for_status()
            users = response.json().get("data", [])

            for user in users:
                user_id = user.get("id")
                user_attributes = user.get("attributes", {})
                self.users_map[user_id] = {
                    "username": user_attributes.get("name", ""),
                    "email": user_attributes.get("email", "")
                }

            logging.debug(logs_format(f"Cached {len(self.users_map)} Rootly users"))
        except requests.RequestException as e:
            logging.error(logs_format(f"Error fetching Rootly users: {str(e)}"))

    def get_on_call_users(self, schedule_id: str) -> List[Dict[str, Any]]:
        """
        Get the current on-call users for a specific schedule.

        Args:
            schedule_id: The ID of the Rootly schedule

        Returns:
            List of user objects with email and other details
        """
        logging.debug(logs_format(f"Getting on-call users from Rootly for schedule {schedule_id}"))

        try:
            # Get current on-call users for the schedule
            response = requests.get(
                f"{self.api_url}/schedules/{schedule_id}",
                headers=self.headers
            )
            response.raise_for_status()

            # Process the response
            return self._process_on_call_response(response.json())

        except requests.RequestException as e:
            logging.error(logs_format(f"Error fetching Rootly on-call users: {str(e)}"))
            return []

    def _process_on_call_response(self, response_data: Any) -> List[Dict[str, Any]]:
        """
        Process the response from the Rootly API to extract on-call users.

        Args:
            response_data: The JSON response from the Rootly API

        Returns:
            List of user objects with email and other details
        """
        result = []

        # Check if we have a valid data object
        if not isinstance(response_data, dict):
            logging.error(logs_format(f"Unexpected response format from Rootly API: {response_data}"))
            return []

        # Get the data array from the response
        on_calls = response_data.get("data", [])

        # Handle case where data is not a list
        if not isinstance(on_calls, list):
            # If data is a single object, wrap it in a list
            if isinstance(on_calls, dict):
                on_calls = [on_calls]
            else:
                logging.error(logs_format(f"Unexpected data format in Rootly API response: {on_calls}"))
                return []

        # Process each on-call entry
        for on_call in on_calls:
            if not isinstance(on_call, dict):
                logging.warning(logs_format(f"Skipping non-dict on_call entry: {on_call}"))
                continue

            # Extract user ID from relationships if available
            relationships = on_call.get("relationships", {})
            if isinstance(relationships, dict):
                owner_user = relationships.get("owner_user", {})
                if isinstance(owner_user, dict):
                    user_data = owner_user.get("data", {})
                    if isinstance(user_data, dict):
                        user_id = user_data.get("id")
                        if user_id and user_id in self.users_map:
                            # Format similar to PagerDuty's response for compatibility
                            user_info = {
                                "id": user_id,
                                "email": self.users_map[user_id]["email"],
                                "name": self.users_map[user_id]["username"]
                            }
                            result.append(user_info)

        logging.info(logs_format(f"Found {len(result)} on-call users"))
        return result
