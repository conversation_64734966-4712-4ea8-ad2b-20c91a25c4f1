from __future__ import annotations
import datetime
import pytz
from humanize.time import precisedel<PERSON>
from slack_sdk import WebClient
from dataclasses import dataclass, fields
from . import settings


statusses = {
    "planned": ":pill:",
    "ongoing": ":wrench:",
    "finished": ":white_check_mark:",
}

timezones = ["GMT", "Europe/Minsk", "Asia/Ho_Chi_Minh", "CET", "US/Eastern"]


class SlackInit:
    # Set only fields that
    # exist in the class
    def __init__(self, **kwargs):
        field_names = set([field.name for field in fields(self)])
        for cls_field, cls_field_value in kwargs.items():
            if cls_field in field_names:
                setattr(self, cls_field, cls_field_value)


@dataclass(init=False)
class SlackProfile(SlackInit):
    email: str
    display_name: str
    manager: SlackUser

    def __init__(self, **kwargs):
        super(SlackProfile, self).__init__(**kwargs)


@dataclass(init=False)
class SlackUser(SlackInit):
    id: str
    profile: SlackProfile
    _tag: str = ""

    @property
    def tag(self):
        if not self._tag:
            self._tag = f"<@{self.id}>"
        return self._tag

    def __init__(self, **kwargs):
        super(SlackUser, self).__init__(**kwargs)


class Slack:
    def __init__(self, token, log=None):
        self.log = log

        self.__slack_client = WebClient(token=token)

    def set_group_users(self, user_ids, group_id=None):
        """Returns True if request was successfull"""
        if group_id:
            return self._set_group_users_by_group_id(user_ids, group_id)

    def get_user(
            self, user_email: str = "", user_id: str = "", manager: bool = False
    ) -> SlackUser:
        if user_email:
            return self._get_user_by_email(user_email, manager)
        elif user_id:
            return self._get_user_by_id(user_id, manager)
        else:
            raise ValueError("Wrong arguments passed to get_user method")

    def get_group(self, group_name):
        """Returns a dict of group attributes"""

        if group_name:
            return self._get_group_by_name(group_name)
        else:
            raise ValueError("Wrong arguments passed to get_group method")

    def get_users_in_group(self, group_name=None, group_id=None):
        """Returns a list of userids"""

        if group_name:
            return self._get_users_by_group_name(group_name)
        elif group_id:
            return self._get_users_by_group_id(group_id)
        else:
            raise ValueError(
                "Wrong arguments passed to get_users_in_group method")

    def _get_group_by_name(self, group_name):
        """Returns a dict of group attributes"""

        group_name = group_name.lstrip("@")
        response = self.__slack_client.usergroups_list(include_users=True)

        for group in response["usergroups"]:
            if group["handle"] == group_name:
                return group

    def _get_user_by_email(self, user_email: str, manager: bool = False) -> SlackUser:

        response = self.__slack_client.users_lookupByEmail(email=user_email)
        response["user"]["profile"] = self._get_user_profile_by_id(
            response["user"]["id"], manager
        )
        return SlackUser(**response["user"])

    def _get_user_by_id(self, user_id: str, manager: bool = False) -> SlackUser:

        response = self.__slack_client.users_info(user=user_id)
        response["user"]["profile"] = self._get_user_profile_by_id(
            user_id, manager)

        return SlackUser(**response["user"])

    def _get_user_profile_by_id(
            self, user_id: str, manager: bool = False
    ) -> SlackProfile:

        response = self.__slack_client.users_profile_get(user=user_id)
        if manager:
            fields = response["profile"]["fields"]
            if fields[settings.SLACK_MANAGER_FIELD_ID]["value"] != settings.SLACK_CEO_USER_ID:
                response["profile"]["manager"] = self._get_user_by_id(
                    fields[settings.SLACK_MANAGER_FIELD_ID]["value"], True
                )

        return SlackProfile(**response["profile"])

    def _get_users_by_group_name(self, group_name):
        """Returns a list of userids"""

        group_name = group_name.lstrip("@")
        response = self.__slack_client.usergroups_list(include_users=True)

        for group in response["usergroups"]:
            if group["handle"] == group_name:
                return group["users"]

    def _get_users_by_group_id(self, group_id):
        """Returns a list of userids"""

        response = self.__slack_client.usergroups_users_list(
            usergroup=group_id)

        return response["users"]

    def _set_group_users_by_group_id(self, user_ids, group_id):
        """Returns True if request was successfull"""

        response = self.__slack_client.usergroups_users_update(
            usergroup=group_id, users=",".join(user_ids)
        )

        return response["ok"]

    def get_maintenance_announcement(self, maintenance, description):
        length = precisedelta(datetime.datetime.strptime(
            maintenance["end_time"], "%Y-%m-%dT%H:%M:%SZ")-datetime.datetime.strptime(maintenance["start_time"], "%Y-%m-%dT%H:%M:%SZ"))
        start_date = datetime.datetime.strptime(
            maintenance["start_time"], '%Y-%m-%dT%H:%M:%SZ')
        end_date = datetime.datetime.strptime(
            maintenance["end_time"], '%Y-%m-%dT%H:%M:%SZ')
        services_title = "\n*Affected services:* "
        times = "\n*Time frame*\n"
        for tz_name in timezones:
            tz = pytz.timezone(tz_name)
            start_tz_date = start_date.astimezone(
                tz).astimezone(tz).strftime('%Y-%m-%d %H:%M:%S')
            end_tz_date = end_date.astimezone(tz).astimezone(
                tz).strftime('%Y-%m-%d %H:%M:%S')

            times += "*%s* - *%s* *%s*\n" % (start_tz_date,
                                             end_tz_date, tz_name)
        times += "*Length:* %s" % length

        maintenance_text = """
{status_icon} *<{html_url}|Maintenance window> is {mw_status}*
*Summary:* {summary}
""".format(**maintenance,
            length=length,
            status_icon=statusses[description],
            mw_status=description
           )
        services = ", ".join("<{html_url}|{summary}>".format(**v)
                             for v in maintenance["services"])
        # We need to fit the message to 4000 chars because of Slack has a bug in chat_update method
        if len(maintenance_text)+len(services)+len(times)+len(services_title) >= 4000:
            services = "Too much to show (%s). Please click on title to get more info." % len(
                maintenance["services"])
        # if it is still more than 4000
        if len(maintenance_text)+len(services)+len(times)+len(services_title) >= 4000:
            maintenance_text = maintenance_text[:3000] + '...'
        maintenance_text += services_title
        maintenance_text += services
        maintenance_text += times
        return maintenance_text

    def send_maintenance_announcement(self, channel, maintenance, maintenance_text, ts=None):
        profile_image = ""
        created_by = maintenance.get("created_by", {}) or {}
        if created_by:
            user = self.__slack_client.users_lookupByEmail(
                email=created_by["email"])
            if user["ok"]:
                profile_image = user["user"]["profile"]["image_24"]
        if ts:
            self.__slack_client.chat_update(
                ts=ts,
                channel=channel,
                text=maintenance_text,
                mrkdwn=True,
                icon_url=profile_image,
                username=created_by.get("username", "SRE team")
            )
        else:
            ts = self.__slack_client.chat_postMessage(
                channel=channel,
                text=maintenance_text,
                mrkdwn=True,
                icon_url=profile_image,
                username=created_by.get("username", "SRE team")
            )["ts"]

        return ts
