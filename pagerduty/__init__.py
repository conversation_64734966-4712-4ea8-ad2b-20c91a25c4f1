from pdpyras import APISession
from collections import defaultdict
import json
import logging
from format import logs_format


class PagerDuty:
    PRIMARY_ONCALL = 1
    SECONDARY_ONCALL = 2

    def __init__(self, token):
        self.__pager_duty = APISession(token)
        self.__pager_duty_slack = APISession(token)
        self.__pager_duty_slack.url = "https://app.pagerduty.com"
        self.policies_response = self.__pager_duty.get("/escalation_policies", params={'limit': 100, 'offset': 0})
        self.escalation_policies = self.policies_response.json()[
            "escalation_policies"]
        self.service_slack_map = defaultdict(set)
        self.users_map = {}
        self.slack_workspace_id = "TBZCSLK7Y"
        self.__populate_cache_slack_channels()
        self.get_pagerduty_users()

    def get_slack_channels(self, service_id):
        return self.service_slack_map.get(service_id, [])

    def get_pagerduty_users(self):
        for user in self.__pager_duty.get("/users").json()["users"]:
            self.users_map[user["id"]] = {
                "username": user["summary"],
                "email": user["email"],
            }

    def get_escalation_policies_ids(self):
        logging.debug(logs_format(
            "Get escalation policies ID's from Paged Duty."))
        escalation_policies_ids = []

        if self.policies_response.ok:
            for escalation_policy in self.escalation_policies:
                escalation_policies_ids.append(escalation_policy["id"])

        return escalation_policies_ids

    def get_escalation_policies_data(self):

        tags = []
        data = '{}'

        logging.debug(logs_format(
            f"Create JSON with escalation policies ID's, names and groups from tags."))
        if self.policies_response.ok:
            for index, escalation_policy in enumerate(self.get_escalation_policies_ids()):

                for policy in self.escalation_policies:
                    if policy["id"] == escalation_policy:
                        logging.debug(logs_format(
                            f"Get escalation policies name for {escalation_policy}."))
                        escalation_policy_name = policy["summary"]

                logging.debug(logs_format(
                    f"Get tags for {escalation_policy_name} escalation policy."))
                tags_response = self.__pager_duty.get(
                    f"/escalation_policies/{escalation_policy}/tags")

                logging.debug(logs_format(
                    f"Collect slack groups for {escalation_policy_name} escalation policy from tags."))
                for item in tags_response.json()["tags"]:
                    if item["label"].startswith("@"):
                        tags.append(item["label"])

                struct = json.loads(data)
                struct.update(
                    {escalation_policy: {"name": escalation_policy_name, "slack_groups": tags}})

                if index == 0:
                    escalation_policies_data = {"esc_pol": struct}

                logging.debug(logs_format(
                    f"Update JSON with escalation policies ID's, names and groups from tags."))
                escalation_policies_data["esc_pol"].update(struct)
                tags = []

            return escalation_policies_data

    def __populate_cache_slack_channels(self):

        w = self.__pager_duty_slack.get(
            "/integration-slack/workspaces/%s/connections" % self.slack_workspace_id)
        data = w.json()
        for item in data.get("slack_connections", []):
            self.service_slack_map[item["source_id"]].add(item["channel_id"])

    def get_on_call(self, level, pd_escalation_policy_id):
        logging.debug(logs_format(
            "Get oncall users from Paged Duty by escalation policy."))

        iteration = 0
        res = []
        br = False
        while not br:
            # Get paginated response and process it
            response = self.__pager_duty.get(
                "/oncalls?limit=100&offset=%s" % (iteration*100))
            if not response.ok or not response.json()["more"]:
                br = True
            if not response.ok:
                break
            oncalls = response.json()["oncalls"]
            for oncall in oncalls:
                if oncall["escalation_level"] == level and \
                        oncall["escalation_policy"]["id"] == pd_escalation_policy_id:
                    tres = self.__pager_duty.get(
                        "/users/" + oncall["user"]["id"])
                    res.append(tres.json()["user"])
            iteration += 1
        return res

    def get_maintenance_windows(self, status):
        br = False
        iteration = 0
        d = {}
        while not br:
            # Get paginated response and process it
            response = self.__pager_duty.get(
                "/maintenance_windows?filter=%s&limit=100&offset=%s" % (status, iteration*100))
            if not response.ok or not response.json()["more"]:
                br = True

            if not response.ok:
                break

            w = response.json()
            for mw in w.get("maintenance_windows"):
                user = {}
                # created_by may be None
                if mw.get("created_by", {}) and mw.get("created_by", {}).get("type", "") == "user_reference":
                    user_id = mw["created_by"]["id"]
                    user = self.users_map.get(user_id)

                d[mw["id"]] = {
                    "summary": mw["summary"],
                    "html_url": mw["html_url"],
                    "start_time": mw["start_time"],
                    "end_time": mw["end_time"],
                    "description": mw["description"],
                    "services": mw["services"],
                    "created_by": user,
                }
            iteration += 1
        return d
